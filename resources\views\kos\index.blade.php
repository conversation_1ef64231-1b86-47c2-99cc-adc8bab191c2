<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="font-bold text-2xl text-gray-900">
                    Browse Kos Properties 🏠
                </h2>
                <p class="text-gray-600 mt-1">Find your perfect place to stay</p>
            </div>
        </div>
    </x-slot>

    <div class="py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Search and Filter Section -->
            <div class="card mb-8">
                <div class="card-body">
                    <form method="GET" action="{{ route('kos.index') }}" class="space-y-6" id="searchForm">
                        <!-- Main Search Bar -->
                        <div class="relative">
                            <input type="text" name="search" value="{{ request('search') }}"
                                   placeholder="Search by name, location, or facilities..."
                                   class="form-input pl-12 text-lg py-4">
                            <div class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                                🔍
                            </div>
                        </div>

                        <!-- Advanced Filters Toggle -->
                        <div class="flex items-center justify-between">
                            <button type="button"
                                    onclick="toggleFilters()"
                                    class="text-gray-700 hover:text-gray-900 font-medium flex items-center space-x-2">
                                <span>Advanced Filters</span>
                                <span id="filterToggleIcon">▼</span>
                            </button>

                            <div class="flex items-center space-x-4">
                                <!-- Sort By -->
                                <div class="flex items-center space-x-2">
                                    <label class="text-sm font-medium text-gray-700">Sort by:</label>
                                    <select name="sort_by" onchange="this.form.submit()" class="form-input py-2 text-sm">
                                        <option value="latest" {{ request('sort_by') == 'latest' ? 'selected' : '' }}>Latest</option>
                                        <option value="price_low" {{ request('sort_by') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                                        <option value="price_high" {{ request('sort_by') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                                        <option value="rating" {{ request('sort_by') == 'rating' ? 'selected' : '' }}>Highest Rated</option>
                                        <option value="name" {{ request('sort_by') == 'name' ? 'selected' : '' }}>Name A-Z</option>
                                    </select>
                                </div>

                                <button type="submit" class="btn-primary">
                                    🔍 Search
                                </button>
                            </div>
                        </div>

                        <!-- Advanced Filters (Hidden by default) -->
                        <div id="advancedFilters" class="hidden border-t border-gray-200 pt-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <!-- Location Filter -->
                                <div>
                                    <label class="form-label">Location</label>
                                    <input type="text" name="location" value="{{ request('location') }}"
                                           placeholder="City or area..." class="form-input">
                                </div>

                                <!-- Price Range -->
                                <div>
                                    <label class="form-label">Price Range</label>
                                    <div class="grid grid-cols-2 gap-2">
                                        <input type="number" name="min_price" value="{{ request('min_price') }}"
                                               placeholder="Min" class="form-input">
                                        <input type="number" name="max_price" value="{{ request('max_price') }}"
                                               placeholder="Max" class="form-input">
                                    </div>
                                </div>

                                <!-- Rating Filter -->
                                <div>
                                    <label class="form-label">Minimum Rating</label>
                                    <select name="min_rating" class="form-input">
                                        <option value="">Any Rating</option>
                                        <option value="4" {{ request('min_rating') == '4' ? 'selected' : '' }}>4+ Stars</option>
                                        <option value="3" {{ request('min_rating') == '3' ? 'selected' : '' }}>3+ Stars</option>
                                        <option value="2" {{ request('min_rating') == '2' ? 'selected' : '' }}>2+ Stars</option>
                                    </select>
                                </div>

                                <!-- Facilities Filter -->
                                <div>
                                    <label class="form-label">Facilities</label>
                                    <div class="relative">
                                        <button type="button"
                                                onclick="toggleFacilities()"
                                                class="form-input w-full text-left flex items-center justify-between">
                                            <span>Select facilities...</span>
                                            <span>▼</span>
                                        </button>
                                        <div id="facilitiesDropdown" class="hidden absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-lg shadow-lg z-10 mt-1 max-h-48 overflow-y-auto">
                                            @foreach($commonFacilities as $facility)
                                                <label class="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer">
                                                    <input type="checkbox"
                                                           name="facilities[]"
                                                           value="{{ $facility }}"
                                                           {{ in_array($facility, request('facilities', [])) ? 'checked' : '' }}
                                                           class="rounded border-gray-300 text-gray-900 mr-2">
                                                    <span class="text-sm">{{ $facility }}</span>
                                                </label>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Price Filters -->
                            <div class="mt-4">
                                <label class="form-label">Quick Price Filters</label>
                                <div class="flex flex-wrap gap-2">
                                    @foreach($priceRanges as $range)
                                        <button type="button"
                                                onclick="setPriceRange({{ $range['min'] }}, {{ $range['max'] ?? 'null' }})"
                                                class="px-3 py-1 text-sm border border-gray-300 rounded-full hover:bg-gray-50 transition-colors duration-200 {{ (request('min_price') == $range['min'] && request('max_price') == $range['max']) ? 'bg-gray-900 border-gray-900 text-white' : '' }}">
                                            {{ $range['label'] }}
                                        </button>
                                    @endforeach
                                </div>
                            </div>

                            <!-- Clear Filters -->
                            @if(request()->hasAny(['search', 'location', 'min_price', 'max_price', 'min_rating', 'facilities']))
                                <div class="mt-4 pt-4 border-t border-gray-200">
                                    <a href="{{ route('kos.index') }}" class="btn-secondary text-sm">
                                        Clear All Filters
                                    </a>
                                </div>
                            @endif
                        </div>
                    </form>
                </div>
            </div>

            <!-- Results Count and Active Filters -->
            <div class="mb-6">
                <div class="flex items-center justify-between">
                    <p class="text-gray-600">
                        Found <strong>{{ $kos->total() }}</strong> properties
                        @if(request('search'))
                            for "<strong>{{ request('search') }}</strong>"
                        @endif
                    </p>

                    <!-- Active Filters -->
                    @if(request()->hasAny(['search', 'location', 'min_price', 'max_price', 'min_rating', 'facilities', 'sort_by']))
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-500">Active filters:</span>
                            <div class="flex flex-wrap gap-2">
                                @if(request('location'))
                                    <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                                        📍 {{ request('location') }}
                                    </span>
                                @endif

                                @if(request('min_price') || request('max_price'))
                                    <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                                        💰 Rp {{ number_format(request('min_price', 0)) }} - {{ request('max_price') ? 'Rp ' . number_format(request('max_price')) : '∞' }}
                                    </span>
                                @endif

                                @if(request('min_rating'))
                                    <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                                        ⭐ {{ request('min_rating') }}+ stars
                                    </span>
                                @endif

                                @if(request('facilities'))
                                    @foreach(request('facilities') as $facility)
                                        <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                                            🏠 {{ $facility }}
                                        </span>
                                    @endforeach
                                @endif

                                @if(request('sort_by') && request('sort_by') !== 'latest')
                                    <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                                        📊 {{ ucfirst(str_replace('_', ' ', request('sort_by'))) }}
                                    </span>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Kos Grid -->
            @if($kos->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    @foreach($kos as $property)
                        <div class="card group hover:shadow-2xl transition-all duration-300">
                            <!-- Image -->
                            <div class="relative h-48 bg-gray-200 overflow-hidden">
                                @if($property->foto && count($property->foto) > 0)
                                    <img src="{{ asset('storage/' . $property->foto[0]) }}" 
                                         alt="{{ $property->nama_kos }}"
                                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                                @else
                                    <div class="w-full h-full flex items-center justify-center bg-gray-100">
                                        <span class="text-4xl text-gray-400">🏠</span>
                                    </div>
                                @endif
                                
                                <!-- Price Badge -->
                                <div class="absolute top-4 right-4 bg-white shadow-sm px-3 py-1 rounded-full border border-gray-200">
                                    <span class="font-bold text-gray-900">Rp {{ number_format($property->harga, 0, ',', '.') }}</span>
                                    <span class="text-xs text-gray-600">/month</span>
                                </div>
                            </div>
                            
                            <div class="card-body">
                                <!-- Title and Location -->
                                <div class="mb-3">
                                    <h3 class="font-bold text-lg text-gray-900 mb-1">{{ $property->nama_kos }}</h3>
                                    <p class="text-gray-600 text-sm flex items-center">
                                        📍 {{ Str::limit($property->alamat, 50) }}
                                    </p>
                                </div>
                                
                                <!-- Facilities -->
                                <div class="mb-4">
                                    <p class="text-gray-700 text-sm">
                                        {{ Str::limit($property->fasilitas, 80) }}
                                    </p>
                                </div>
                                
                                <!-- Rating and Reviews -->
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center space-x-2">
                                        @if($property->reviews->count() > 0)
                                            <div class="flex items-center">
                                                <span class="text-yellow-400">⭐</span>
                                                <span class="font-medium text-gray-900 ml-1">
                                                    {{ number_format($property->averageRating(), 1) }}
                                                </span>
                                                <span class="text-gray-600 text-sm ml-1">
                                                    ({{ $property->reviews->count() }} reviews)
                                                </span>
                                            </div>
                                        @else
                                            <span class="text-gray-500 text-sm">No reviews yet</span>
                                        @endif
                                    </div>
                                </div>
                                
                                <!-- Action Button -->
                                <a href="{{ route('kos.show', $property) }}" 
                                   class="btn-primary w-full text-center block">
                                    View Details
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
                
                <!-- Pagination -->
                <div class="flex justify-center">
                    {{ $kos->withQueryString()->links() }}
                </div>
            @else
                <!-- Empty State -->
                <div class="card">
                    <div class="card-body text-center py-12">
                        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <span class="text-gray-400 text-4xl">🔍</span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">No properties found</h3>
                        <p class="text-gray-600 mb-6">
                            @if(request()->hasAny(['search', 'min_price', 'max_price']))
                                Try adjusting your search criteria or browse all available properties.
                            @else
                                There are no kos properties available at the moment.
                            @endif
                        </p>
                        @if(request()->hasAny(['search', 'min_price', 'max_price']))
                            <a href="{{ route('kos.index') }}" class="btn-secondary">
                                Clear Filters
                            </a>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>

    <script>
        function toggleFilters() {
            const filters = document.getElementById('advancedFilters');
            const icon = document.getElementById('filterToggleIcon');

            if (filters.classList.contains('hidden')) {
                filters.classList.remove('hidden');
                icon.textContent = '▲';
            } else {
                filters.classList.add('hidden');
                icon.textContent = '▼';
            }
        }

        function toggleFacilities() {
            const dropdown = document.getElementById('facilitiesDropdown');
            dropdown.classList.toggle('hidden');
        }

        function setPriceRange(min, max) {
            document.querySelector('input[name="min_price"]').value = min;
            document.querySelector('input[name="max_price"]').value = max || '';
            document.getElementById('searchForm').submit();
        }

        // Close facilities dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('facilitiesDropdown');
            const button = event.target.closest('button');

            if (!button || button.getAttribute('onclick') !== 'toggleFacilities()') {
                dropdown.classList.add('hidden');
            }
        });

        // Show advanced filters if any advanced filter is active
        document.addEventListener('DOMContentLoaded', function() {
            const hasAdvancedFilters = {{ request()->hasAny(['location', 'min_price', 'max_price', 'min_rating', 'facilities']) ? 'true' : 'false' }};
            if (hasAdvancedFilters) {
                toggleFilters();
            }
        });
    </script>
</x-app-layout>

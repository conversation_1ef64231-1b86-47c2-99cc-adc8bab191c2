<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex items-center justify-between">
            <div>
                <h2 class="font-bold text-2xl text-gray-900">
                    Edit Property ✏️
                </h2>
                <p class="text-gray-600 mt-1">Update your kos property details</p>
            </div>
            <div class="hidden sm:flex items-center space-x-3 px-6">
                <a href="<?php echo e(route('owner.kos.show', $kos)); ?>" class="btn-secondary">
                    ← Back to Property
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-8">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <form action="<?php echo e(route('owner.kos.update', $kos)); ?>" method="POST" enctype="multipart/form-data" class="space-y-6">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                
                <!-- Basic Information -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold text-gray-900">Basic Information</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <!-- Property Name -->
                        <div>
                            <label for="nama_kos" class="form-label">Property Name *</label>
                            <input type="text" 
                                   id="nama_kos" 
                                   name="nama_kos" 
                                   value="<?php echo e(old('nama_kos', $kos->nama_kos)); ?>"
                                   class="form-input" 
                                   placeholder="e.g., Kos Melati Indah"
                                   required>
                            <?php $__errorArgs = ['nama_kos'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Address -->
                        <div>
                            <label for="alamat" class="form-label">Full Address *</label>
                            <textarea id="alamat" 
                                      name="alamat" 
                                      rows="3"
                                      class="form-input" 
                                      placeholder="Enter the complete address including street, district, city"
                                      required><?php echo e(old('alamat', $kos->alamat)); ?></textarea>
                            <?php $__errorArgs = ['alamat'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Price -->
                        <div>
                            <label for="harga" class="form-label">Monthly Rent (Rp) *</label>
                            <input type="number"
                                   id="harga"
                                   name="harga"
                                   value="<?php echo e(old('harga', $kos->harga)); ?>"
                                   class="form-input"
                                   placeholder="e.g., 1500000"
                                   min="0"
                                   max="999999999999.99"
                                   step="0.01"
                                   required>
                            <p class="mt-1 text-sm text-gray-600">
                                Enter the monthly rent in Indonesian Rupiah (Rp). Example: 1500000 for Rp 1.5 million.
                            </p>
                            <?php $__errorArgs = ['harga'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Facilities & Amenities -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold text-gray-900">Facilities & Amenities</h3>
                    </div>
                    <div class="card-body">
                        <div>
                            <label for="fasilitas" class="form-label">Facilities Description *</label>
                            <textarea id="fasilitas" 
                                      name="fasilitas" 
                                      rows="5"
                                      class="form-input" 
                                      placeholder="Describe all facilities and amenities available (e.g., WiFi, AC, private bathroom, kitchen, parking, etc.)"
                                      required><?php echo e(old('fasilitas', $kos->fasilitas)); ?></textarea>
                            <?php $__errorArgs = ['fasilitas'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Current Photos -->
                <?php if($kos->foto && count($kos->foto) > 0): ?>
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold text-gray-900">Current Photos</h3>
                    </div>
                    <div class="card-body">
                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                            <?php $__currentLoopData = $kos->foto; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $photo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="relative group">
                                    <img src="<?php echo e(asset('storage/' . $photo)); ?>" 
                                         alt="Property photo"
                                         class="w-full h-24 object-cover rounded-lg">
                                    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
                                        <span class="text-white text-xs">Current Photo</span>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Add New Photos -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold text-gray-900">Add New Photos</h3>
                    </div>
                    <div class="card-body">
                        <div>
                            <label for="foto" class="form-label">Upload Additional Photos</label>
                            <input type="file" 
                                   id="foto" 
                                   name="foto[]" 
                                   multiple
                                   accept="image/*"
                                   class="form-input">
                            <p class="mt-1 text-sm text-gray-600">
                                You can upload additional photos. Supported formats: JPEG, PNG, JPG, GIF. Max size: 2MB per photo.
                            </p>
                            <?php $__errorArgs = ['foto'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <?php $__errorArgs = ['foto.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Location (Optional) -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold text-gray-900">Location (Optional)</h3>
                    </div>
                    <div class="card-body">
                        <div class="space-y-4">
                            <!-- Google Maps URL Input -->
                            <div>
                                <label for="google_maps_url" class="form-label">Google Maps Link</label>
                                <input type="url"
                                       id="google_maps_url"
                                       name="google_maps_url"
                                       value="<?php echo e(old('google_maps_url', $kos->google_maps_url)); ?>"
                                       class="form-input"
                                       placeholder="https://maps.google.com/...">
                                <?php $__errorArgs = ['google_maps_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <p class="mt-1 text-sm text-gray-600">
                                    Paste a Google Maps link to your property location. This will help tenants find your property easily.
                                </p>
                            </div>


                        </div>

                        <!-- Instructions -->
                        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                            <h4 class="font-medium text-blue-900 mb-2">How to get a Google Maps link:</h4>
                            <ol class="text-sm text-blue-800 space-y-1">
                                <li>1. Go to <a href="https://maps.google.com" target="_blank" class="underline">Google Maps</a></li>
                                <li>2. Search for your property address</li>
                                <li>3. Click on the location pin</li>
                                <li>4. Click "Share" and copy the link</li>
                                <li>5. Paste the link in the field above</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <!-- Status -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold text-gray-900">Availability Status</h3>
                    </div>
                    <div class="card-body">
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="status" 
                                   name="status" 
                                   value="1"
                                   <?php echo e(old('status', $kos->status) ? 'checked' : ''); ?>

                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:ring-blue-500">
                            <label for="status" class="ml-2 text-sm text-gray-700">
                                Property is available for rent
                            </label>
                        </div>
                        <p class="mt-1 text-sm text-gray-600">
                            Uncheck this if the property is not ready for rent yet.
                        </p>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-end space-x-4">
                    <a href="<?php echo e(route('owner.kos.show', $kos)); ?>" class="btn-secondary">
                        Cancel
                    </a>
                    <button type="submit" class="btn-primary">
                        Update Property
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const googleMapsInput = document.getElementById('google_maps_url');

            // Validate Google Maps URL
            googleMapsInput.addEventListener('blur', function() {
                const url = this.value.trim();
                if (url && !isValidGoogleMapsUrl(url)) {
                    this.classList.add('border-red-500');
                    showValidationMessage(this, 'Please enter a valid Google Maps URL');
                } else {
                    this.classList.remove('border-red-500');
                    hideValidationMessage(this);
                }
            });

            function isValidGoogleMapsUrl(url) {
                const validDomains = [
                    'maps.google.com',
                    'www.google.com/maps',
                    'google.com/maps',
                    'maps.app.goo.gl',
                    'goo.gl/maps'
                ];

                try {
                    const urlObj = new URL(url);
                    return validDomains.some(domain => urlObj.hostname.includes(domain));
                } catch {
                    return false;
                }
            }

            function showValidationMessage(input, message) {
                hideValidationMessage(input);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'mt-1 text-sm text-red-600 validation-error';
                errorDiv.textContent = message;
                input.parentNode.appendChild(errorDiv);
            }

            function hideValidationMessage(input) {
                const existingError = input.parentNode.querySelector('.validation-error');
                if (existingError) {
                    existingError.remove();
                }
            }
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\Rentalin\resources\views/owner/kos/edit.blade.php ENDPATH**/ ?>
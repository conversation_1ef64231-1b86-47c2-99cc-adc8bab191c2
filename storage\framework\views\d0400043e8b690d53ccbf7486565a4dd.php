<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex items-center justify-between">
            <div>
                <h2 class="font-bold text-2xl text-gray-900">
                    <?php echo e($kos->nama_kos); ?>

                </h2>
                <p class="text-gray-600 mt-1 flex items-center">
                    📍 <?php echo e($kos->alamat); ?>

                </p>
            </div>
            <div class="hidden sm:flex items-center space-x-3">
                <a href="<?php echo e(route('kos.index')); ?>" class="btn-secondary">
                    ← Back to Browse
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Success/Error Messages -->
            <?php if(session('success')): ?>
                <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <?php echo e(session('error')); ?>

                </div>
            <?php endif; ?>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Image Gallery -->
                    <div class="card">
                        <div class="relative h-96 bg-gray-200 overflow-hidden rounded-t-xl">
                            <?php if($kos->foto && count($kos->foto) > 0): ?>
                                <img src="<?php echo e(asset('storage/' . $kos->foto[0])); ?>" 
                                     alt="<?php echo e($kos->nama_kos); ?>"
                                     class="w-full h-full object-cover">
                                
                                <?php if(count($kos->foto) > 1): ?>
                                    <div class="absolute bottom-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                                        +<?php echo e(count($kos->foto) - 1); ?> more photos
                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-purple-100">
                                    <span class="text-6xl">🏠</span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Property Details -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-xl font-bold text-gray-900">Property Details</h3>
                        </div>
                        <div class="card-body">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-2">Monthly Rent</h4>
                                    <p class="text-2xl font-bold text-blue-600">
                                        Rp <?php echo e(number_format($kos->harga, 0, ',', '.')); ?>

                                        <span class="text-sm text-gray-600 font-normal">/month</span>
                                    </p>
                                </div>
                                
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-2">Status</h4>
                                    <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo e($kos->status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                        <?php echo e($kos->status ? 'Available' : 'Not Available'); ?>

                                    </span>
                                </div>
                            </div>
                            
                            <div class="mt-6">
                                <h4 class="font-semibold text-gray-900 mb-2">Facilities & Amenities</h4>
                                <p class="text-gray-700 leading-relaxed"><?php echo e($kos->fasilitas); ?></p>
                            </div>
                            
                            <div class="mt-6">
                                <h4 class="font-semibold text-gray-900 mb-2">Owner Information</h4>
                                <div class="flex items-center space-x-3">
                                    <?php if($kos->owner): ?>
                                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold">
                                            <?php echo e(substr($kos->owner->name, 0, 1)); ?>

                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900"><?php echo e($kos->owner->name); ?></p>
                                            <p class="text-sm text-gray-600">Property Owner</p>
                                        </div>
                                    <?php else: ?>
                                        <div class="w-10 h-10 bg-gray-400 rounded-full flex items-center justify-center text-white font-bold">
                                            ?
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">Owner information not available</p>
                                            <p class="text-sm text-gray-600">Property Owner</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reviews Section -->
                    <div class="card">
                        <div class="card-header">
                            <div class="flex items-center justify-between">
                                <h3 class="text-xl font-bold text-gray-900">Reviews & Ratings</h3>
                                <?php if($totalReviews > 0): ?>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-yellow-400 text-lg">⭐</span>
                                        <span class="font-bold text-gray-900"><?php echo e(number_format($averageRating, 1)); ?></span>
                                        <span class="text-gray-600">(<?php echo e($totalReviews); ?> reviews)</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if($kos->reviews->count() > 0): ?>
                                <div class="space-y-4">
                                    <?php $__currentLoopData = $kos->reviews->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="border-b border-gray-200 pb-4 last:border-b-0">
                                            <div class="flex items-start space-x-3">
                                                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                                                    <?php echo e($review->user ? substr($review->user->name, 0, 1) : '?'); ?>

                                                </div>
                                                <div class="flex-1">
                                                    <div class="flex items-center justify-between mb-1">
                                                        <h5 class="font-medium text-gray-900"><?php echo e($review->user ? $review->user->name : 'Anonymous User'); ?></h5>
                                                        <div class="flex items-center">
                                                            <?php for($i = 1; $i <= 5; $i++): ?>
                                                                <span class="text-sm <?php echo e($i <= $review->rating ? 'text-yellow-400' : 'text-gray-300'); ?>">⭐</span>
                                                            <?php endfor; ?>
                                                        </div>
                                                    </div>
                                                    <p class="text-gray-700 text-sm"><?php echo e($review->ulasan); ?></p>
                                                    <p class="text-xs text-gray-500 mt-1"><?php echo e($review->tanggal->format('M d, Y')); ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-8">
                                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <span class="text-gray-400 text-2xl">⭐</span>
                                    </div>
                                    <p class="text-gray-500">No reviews yet</p>
                                    <p class="text-sm text-gray-400 mt-1">Be the first to review this property</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Add Review Form (Only for tenants with confirmed bookings) -->
                    <?php if(auth()->guard()->check()): ?>
                        <?php if(Auth::user()->isTenant()): ?>
                            <?php
                                $hasBooking = Auth::user()->bookings()
                                    ->where('kos_id', $kos->id)
                                    ->where('status_pemesanan', 'confirmed')
                                    ->exists();

                                $hasReview = Auth::user()->reviews()
                                    ->where('kos_id', $kos->id)
                                    ->exists();
                            ?>

                            <?php if($hasBooking && !$hasReview): ?>
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="text-xl font-bold text-gray-900">Write a Review</h3>
                                    </div>
                                    <div class="card-body">
                                        <form action="<?php echo e(route('reviews.store')); ?>" method="POST" class="space-y-4">
                                            <?php echo csrf_field(); ?>
                                            <input type="hidden" name="kos_id" value="<?php echo e($kos->id); ?>">

                                            <!-- Rating -->
                                            <div>
                                                <label class="form-label">Rating *</label>
                                                <div class="flex items-center space-x-2">
                                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                                        <button type="button"
                                                                onclick="setRating(<?php echo e($i); ?>)"
                                                                class="star-btn text-2xl text-gray-300 hover:text-yellow-400 transition-colors duration-200"
                                                                data-rating="<?php echo e($i); ?>">
                                                            ⭐
                                                        </button>
                                                    <?php endfor; ?>
                                                </div>
                                                <input type="hidden" name="rating" id="rating" value="<?php echo e(old('rating')); ?>">
                                                <?php $__errorArgs = ['rating'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>

                                            <!-- Review Text -->
                                            <div>
                                                <label for="ulasan" class="form-label">Your Review *</label>
                                                <textarea id="ulasan"
                                                          name="ulasan"
                                                          rows="4"
                                                          class="form-input"
                                                          placeholder="Share your experience with this property..."
                                                          required><?php echo e(old('ulasan')); ?></textarea>
                                                <?php $__errorArgs = ['ulasan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>

                                            <button type="submit" class="btn-primary">
                                                Submit Review
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            <?php elseif($hasReview): ?>
                                <div class="card">
                                    <div class="card-body">
                                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                            <div class="flex items-center">
                                                <span class="text-blue-600 text-xl mr-3">✓</span>
                                                <div>
                                                    <h4 class="font-medium text-blue-800">Review Submitted</h4>
                                                    <p class="text-blue-700 text-sm mt-1">
                                                        Thank you for reviewing this property! Your review helps other tenants make informed decisions.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Booking Card -->
                    <?php if($kos->status): ?>
                        <div class="card sticky top-24">
                            <div class="card-header">
                                <h3 class="text-lg font-bold text-gray-900">Book This Property</h3>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-6">
                                    <p class="text-3xl font-bold text-blue-600">
                                        Rp <?php echo e(number_format($kos->harga, 0, ',', '.')); ?>

                                    </p>
                                    <p class="text-gray-600">per month</p>
                                </div>
                                
                                <form action="<?php echo e(route('bookings.store')); ?>" method="POST" class="space-y-4" id="bookingForm">
                                    <?php echo csrf_field(); ?>
                                    <input type="hidden" name="kos_id" value="<?php echo e($kos->id); ?>">

                                    <div>
                                        <label class="form-label">Check-in Date</label>
                                        <input type="date"
                                               name="tanggal_mulai"
                                               class="form-input"
                                               min="<?php echo e(date('Y-m-d')); ?>"
                                               value="<?php echo e(old('tanggal_mulai')); ?>"
                                               required>
                                        <?php $__errorArgs = ['tanggal_mulai'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div>
                                        <label class="form-label">Duration (months)</label>
                                        <select name="durasi" class="form-input" required onchange="calculateTotal()">
                                            <option value="">Select duration</option>
                                            <option value="1" <?php echo e(old('durasi') == '1' ? 'selected' : ''); ?>>1 month</option>
                                            <option value="3" <?php echo e(old('durasi') == '3' ? 'selected' : ''); ?>>3 months</option>
                                            <option value="6" <?php echo e(old('durasi') == '6' ? 'selected' : ''); ?>>6 months</option>
                                            <option value="12" <?php echo e(old('durasi') == '12' ? 'selected' : ''); ?>>12 months</option>
                                            <option value="24" <?php echo e(old('durasi') == '24' ? 'selected' : ''); ?>>24 months</option>
                                        </select>
                                        <?php $__errorArgs = ['durasi'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <!-- Total Price Display -->
                                    <div id="totalPrice" class="hidden bg-gray-50 p-3 rounded-lg">
                                        <div class="flex justify-between items-center">
                                            <span class="text-gray-700">Total Price:</span>
                                            <span class="font-bold text-lg text-blue-600" id="totalAmount">Rp 0</span>
                                        </div>
                                    </div>

                                    <button type="submit" class="btn-primary w-full py-3">
                                        Book Now
                                    </button>
                                </form>
                                
                                <div class="mt-4 text-center">
                                    <p class="text-xs text-gray-500">
                                        You won't be charged yet. Review your booking details first.
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="text-red-600 text-2xl">❌</span>
                                </div>
                                <h3 class="font-bold text-gray-900 mb-2">Not Available</h3>
                                <p class="text-gray-600 text-sm">This property is currently not available for booking.</p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Map Card -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-lg font-bold text-gray-900">Location</h3>
                        </div>
                        <div class="card-body">
                            <?php if($kos->hasValidGoogleMapsUrl()): ?>
                                <!-- Google Maps Embed -->
                                <div class="h-64 bg-gray-200 rounded-lg overflow-hidden mb-3">
                                    <iframe
                                        src="<?php echo e($kos->getGoogleMapsEmbedUrl(600, 256)); ?>"
                                        width="100%"
                                        height="256"
                                        style="border:0;"
                                        allowfullscreen=""
                                        loading="lazy"
                                        referrerpolicy="no-referrer-when-downgrade"
                                        class="rounded-lg">
                                    </iframe>
                                </div>

                                <!-- Map Actions -->
                                <div class="flex flex-wrap gap-2 mb-3">
                                    <a href="<?php echo e($kos->getGoogleMapsUrl()); ?>"
                                       target="_blank"
                                       class="inline-flex items-center px-3 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                                        🗺️ Open in Google Maps
                                    </a>
                                    <button onclick="getDirections()"
                                            class="inline-flex items-center px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
                                        🧭 Get Directions
                                    </button>
                                </div>

                            <?php else: ?>
                                <!-- No location data -->
                                <div class="h-48 bg-gray-100 rounded-lg flex items-center justify-center mb-3">
                                    <div class="text-center">
                                        <span class="text-4xl mb-2 block text-gray-400">📍</span>
                                        <p class="text-gray-500 text-sm">Location information not available</p>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Address -->
                            <div class="bg-gray-50 rounded-lg p-3">
                                <h4 class="font-medium text-gray-900 mb-1">Address</h4>
                                <p class="text-gray-700 text-sm"><?php echo e($kos->alamat); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function calculateTotal() {
            const durasi = document.querySelector('select[name="durasi"]').value;
            const monthlyPrice = <?php echo e($kos->harga); ?>;
            const totalPriceDiv = document.getElementById('totalPrice');
            const totalAmountSpan = document.getElementById('totalAmount');

            if (durasi) {
                const total = monthlyPrice * parseInt(durasi);
                totalAmountSpan.textContent = 'Rp ' + total.toLocaleString('id-ID');
                totalPriceDiv.classList.remove('hidden');
            } else {
                totalPriceDiv.classList.add('hidden');
            }
        }

        // Auto-calculate on page load if duration is already selected
        document.addEventListener('DOMContentLoaded', function() {
            calculateTotal();
        });

        // Star rating functionality
        function setRating(rating) {
            document.getElementById('rating').value = rating;

            // Update star display
            const stars = document.querySelectorAll('.star-btn');
            stars.forEach((star, index) => {
                if (index < rating) {
                    star.classList.remove('text-gray-300');
                    star.classList.add('text-yellow-400');
                } else {
                    star.classList.remove('text-yellow-400');
                    star.classList.add('text-gray-300');
                }
            });
        }

        // Initialize rating on page load if there's an old value
        document.addEventListener('DOMContentLoaded', function() {
            const oldRating = <?php echo e(old('rating', 0)); ?>;
            if (oldRating > 0) {
                setRating(oldRating);
            }
        });

        // Get directions function
        function getDirections() {
            <?php if($kos->hasValidGoogleMapsUrl()): ?>
                const mapUrl = "<?php echo e($kos->getGoogleMapsUrl()); ?>";
                const directionsUrl = mapUrl.replace('maps?q=', 'maps/dir//');
                window.open(directionsUrl, '_blank');
            <?php endif; ?>
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\Rentalin\resources\views/kos/show.blade.php ENDPATH**/ ?>
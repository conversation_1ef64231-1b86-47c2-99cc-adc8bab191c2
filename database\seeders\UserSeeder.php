<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin/test users
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => true, // Owner
            'email_verified_at' => now(),
        ]);

        User::create([
            'name' => 'Test Owner',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => true, // Owner
            'email_verified_at' => now(),
        ]);

        User::create([
            'name' => 'Test Tenant',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => false, // Tenant
            'email_verified_at' => now(),
        ]);

        // Create sample owners
        $owners = [
            [
                'name' => '<PERSON><PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Siti Nurhaliza',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Ahmad Wijaya',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Dewi Sartika',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Rudi Hermawan',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Maya Sari',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Eko Prasetyo',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Rina Kusuma',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => true,
                'email_verified_at' => now(),
            ],
        ];

        foreach ($owners as $owner) {
            User::create($owner);
        }

        // Create sample tenants
        $tenants = [
            [
                'name' => 'Andi Pratama',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Lisa Permata',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Doni Setiawan',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Fitri Handayani',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Agus Salim',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Indira Sari',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Bayu Nugroho',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Citra Dewi',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Fajar Ramadhan',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Gita Savitri',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Hendra Gunawan',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Ika Putri',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Joko Widodo',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Kartika Sari',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => false,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Lukman Hakim',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => false,
                'email_verified_at' => now(),
            ],
        ];

        foreach ($tenants as $tenant) {
            User::create($tenant);
        }

        $this->command->info('Created ' . count($owners) . ' owner users and ' . count($tenants) . ' tenant users.');
        $this->command->info('All users have password: "password"');
    }
}

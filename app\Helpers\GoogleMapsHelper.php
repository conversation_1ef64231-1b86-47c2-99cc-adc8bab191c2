<?php

namespace App\Helpers;

class GoogleMapsHelper
{
    /**
     * Extract coordinates from Google Maps URL
     * Supports various Google Maps URL formats
     */
    public static function extractCoordinatesFromUrl($url)
    {
        if (empty($url)) {
            return null;
        }

        // Pattern 1: @lat,lng,zoom format
        if (preg_match('/@(-?\d+\.?\d*),(-?\d+\.?\d*)/', $url, $matches)) {
            return [
                'latitude' => (float) $matches[1],
                'longitude' => (float) $matches[2]
            ];
        }

        // Pattern 2: ll=lat,lng format
        if (preg_match('/ll=(-?\d+\.?\d*),(-?\d+\.?\d*)/', $url, $matches)) {
            return [
                'latitude' => (float) $matches[1],
                'longitude' => (float) $matches[2]
            ];
        }

        // Pattern 3: q=lat,lng format
        if (preg_match('/q=(-?\d+\.?\d*),(-?\d+\.?\d*)/', $url, $matches)) {
            return [
                'latitude' => (float) $matches[1],
                'longitude' => (float) $matches[2]
            ];
        }

        // Pattern 4: place_id or address-based URLs - return null as we can't extract coordinates
        return null;
    }

    /**
     * Generate Google Maps embed URL for iframe
     */
    public static function generateEmbedUrl($googleMapsUrl, $width = 600, $height = 450)
    {
        if (empty($googleMapsUrl)) {
            return null;
        }

        // Extract coordinates from the URL
        $coordinates = self::extractCoordinatesFromUrl($googleMapsUrl);
        
        if ($coordinates) {
            // Use coordinates for embed
            $lat = $coordinates['latitude'];
            $lng = $coordinates['longitude'];
            return "https://www.google.com/maps/embed/v1/view?key=" . config('services.google_maps.api_key', '') . "&center={$lat},{$lng}&zoom=15";
        }

        // If we can't extract coordinates, try to use the original URL for embed
        // This is a fallback that might not always work
        $encodedUrl = urlencode($googleMapsUrl);
        return "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3966.0!2d0!3d0!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zM!5e0!3m2!1sen!2sid!4v1234567890123!5m2!1sen!2sid&q={$encodedUrl}";
    }

    /**
     * Generate a clean Google Maps link for opening in new tab
     */
    public static function generateCleanMapUrl($googleMapsUrl)
    {
        if (empty($googleMapsUrl)) {
            return null;
        }

        // Extract coordinates from the URL
        $coordinates = self::extractCoordinatesFromUrl($googleMapsUrl);
        
        if ($coordinates) {
            $lat = $coordinates['latitude'];
            $lng = $coordinates['longitude'];
            return "https://www.google.com/maps?q={$lat},{$lng}";
        }

        // Return the original URL if we can't extract coordinates
        return $googleMapsUrl;
    }

    /**
     * Validate if a URL is a valid Google Maps URL
     */
    public static function isValidGoogleMapsUrl($url)
    {
        if (empty($url)) {
            return false;
        }

        // Check if it's a Google Maps domain
        $validDomains = [
            'maps.google.com',
            'www.google.com/maps',
            'google.com/maps',
            'maps.app.goo.gl',
            'goo.gl/maps'
        ];

        $parsedUrl = parse_url($url);
        if (!$parsedUrl || !isset($parsedUrl['host'])) {
            return false;
        }

        $host = strtolower($parsedUrl['host']);
        
        foreach ($validDomains as $domain) {
            if (strpos($host, $domain) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get a thumbnail image URL for the location
     */
    public static function getStaticMapThumbnail($googleMapsUrl, $width = 400, $height = 300, $zoom = 15)
    {
        $coordinates = self::extractCoordinatesFromUrl($googleMapsUrl);
        
        if (!$coordinates) {
            return null;
        }

        $lat = $coordinates['latitude'];
        $lng = $coordinates['longitude'];
        $apiKey = config('services.google_maps.api_key', '');

        if (empty($apiKey)) {
            return null;
        }

        return "https://maps.googleapis.com/maps/api/staticmap?center={$lat},{$lng}&zoom={$zoom}&size={$width}x{$height}&maptype=roadmap&markers=color:red%7C{$lat},{$lng}&key={$apiKey}";
    }
}

<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex items-center justify-between">
            <div>
                <h2 class="font-bold text-2xl text-gray-900">
                    Browse Kos Properties 🏠
                </h2>
                <p class="text-gray-600 mt-1">Find your perfect place to stay</p>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Search and Filter Section -->
            <div class="card mb-8">
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('kos.index')); ?>" class="space-y-6" id="searchForm">
                        <!-- Main Search Bar -->
                        <div class="relative">
                            <input type="text" name="search" value="<?php echo e(request('search')); ?>"
                                   placeholder="Search by name, location, or facilities..."
                                   class="form-input pl-12 text-lg py-4">
                            <div class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                                🔍
                            </div>
                        </div>

                        <!-- Advanced Filters Toggle -->
                        <div class="flex items-center justify-between">
                            <button type="button"
                                    onclick="toggleFilters()"
                                    class="text-gray-700 hover:text-gray-900 font-medium flex items-center space-x-2">
                                <span>Advanced Filters</span>
                                <span id="filterToggleIcon">▼</span>
                            </button>

                            <div class="flex items-center space-x-4">
                                <!-- Sort By -->
                                <div class="flex items-center space-x-2">
                                    <label class="text-sm font-medium text-gray-700">Sort by:</label>
                                    <select name="sort_by" onchange="this.form.submit()" class="form-input py-2 text-sm">
                                        <option value="latest" <?php echo e(request('sort_by') == 'latest' ? 'selected' : ''); ?>>Latest</option>
                                        <option value="price_low" <?php echo e(request('sort_by') == 'price_low' ? 'selected' : ''); ?>>Price: Low to High</option>
                                        <option value="price_high" <?php echo e(request('sort_by') == 'price_high' ? 'selected' : ''); ?>>Price: High to Low</option>
                                        <option value="rating" <?php echo e(request('sort_by') == 'rating' ? 'selected' : ''); ?>>Highest Rated</option>
                                        <option value="name" <?php echo e(request('sort_by') == 'name' ? 'selected' : ''); ?>>Name A-Z</option>
                                    </select>
                                </div>

                                <button type="submit" class="btn-primary">
                                    🔍 Search
                                </button>
                            </div>
                        </div>

                        <!-- Advanced Filters (Hidden by default) -->
                        <div id="advancedFilters" class="hidden border-t border-gray-200 pt-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <!-- Location Filter -->
                                <div>
                                    <label class="form-label">Location</label>
                                    <input type="text" name="location" value="<?php echo e(request('location')); ?>"
                                           placeholder="City or area..." class="form-input">
                                </div>

                                <!-- Price Range -->
                                <div>
                                    <label class="form-label">Price Range</label>
                                    <div class="grid grid-cols-2 gap-2">
                                        <input type="number" name="min_price" value="<?php echo e(request('min_price')); ?>"
                                               placeholder="Min" class="form-input">
                                        <input type="number" name="max_price" value="<?php echo e(request('max_price')); ?>"
                                               placeholder="Max" class="form-input">
                                    </div>
                                </div>

                                <!-- Rating Filter -->
                                <div>
                                    <label class="form-label">Minimum Rating</label>
                                    <select name="min_rating" class="form-input">
                                        <option value="">Any Rating</option>
                                        <option value="4" <?php echo e(request('min_rating') == '4' ? 'selected' : ''); ?>>4+ Stars</option>
                                        <option value="3" <?php echo e(request('min_rating') == '3' ? 'selected' : ''); ?>>3+ Stars</option>
                                        <option value="2" <?php echo e(request('min_rating') == '2' ? 'selected' : ''); ?>>2+ Stars</option>
                                    </select>
                                </div>

                                <!-- Facilities Filter -->
                                <div>
                                    <label class="form-label">Facilities</label>
                                    <div class="relative">
                                        <button type="button"
                                                onclick="toggleFacilities()"
                                                class="form-input w-full text-left flex items-center justify-between">
                                            <span>Select facilities...</span>
                                            <span>▼</span>
                                        </button>
                                        <div id="facilitiesDropdown" class="hidden absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-lg shadow-lg z-10 mt-1 max-h-48 overflow-y-auto">
                                            <?php $__currentLoopData = $commonFacilities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $facility): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <label class="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer">
                                                    <input type="checkbox"
                                                           name="facilities[]"
                                                           value="<?php echo e($facility); ?>"
                                                           <?php echo e(in_array($facility, request('facilities', [])) ? 'checked' : ''); ?>

                                                           class="rounded border-gray-300 text-gray-900 mr-2">
                                                    <span class="text-sm"><?php echo e($facility); ?></span>
                                                </label>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Price Filters -->
                            <div class="mt-4">
                                <label class="form-label">Quick Price Filters</label>
                                <div class="flex flex-wrap gap-2">
                                    <?php $__currentLoopData = $priceRanges; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $range): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <button type="button"
                                                onclick="setPriceRange(<?php echo e($range['min']); ?>, <?php echo e($range['max'] ?? 'null'); ?>)"
                                                class="px-3 py-1 text-sm border border-gray-300 rounded-full hover:bg-gray-50 transition-colors duration-200 <?php echo e((request('min_price') == $range['min'] && request('max_price') == $range['max']) ? 'bg-gray-900 border-gray-900 text-white' : ''); ?>">
                                            <?php echo e($range['label']); ?>

                                        </button>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>

                            <!-- Clear Filters -->
                            <?php if(request()->hasAny(['search', 'location', 'min_price', 'max_price', 'min_rating', 'facilities'])): ?>
                                <div class="mt-4 pt-4 border-t border-gray-200">
                                    <a href="<?php echo e(route('kos.index')); ?>" class="btn-secondary text-sm">
                                        Clear All Filters
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Results Count and Active Filters -->
            <div class="mb-6">
                <div class="flex items-center justify-between">
                    <p class="text-gray-600">
                        Found <strong><?php echo e($kos->total()); ?></strong> properties
                        <?php if(request('search')): ?>
                            for "<strong><?php echo e(request('search')); ?></strong>"
                        <?php endif; ?>
                    </p>

                    <!-- Active Filters -->
                    <?php if(request()->hasAny(['search', 'location', 'min_price', 'max_price', 'min_rating', 'facilities', 'sort_by'])): ?>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-500">Active filters:</span>
                            <div class="flex flex-wrap gap-2">
                                <?php if(request('location')): ?>
                                    <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                                        📍 <?php echo e(request('location')); ?>

                                    </span>
                                <?php endif; ?>

                                <?php if(request('min_price') || request('max_price')): ?>
                                    <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                                        💰 Rp <?php echo e(number_format(request('min_price', 0))); ?> - <?php echo e(request('max_price') ? 'Rp ' . number_format(request('max_price')) : '∞'); ?>

                                    </span>
                                <?php endif; ?>

                                <?php if(request('min_rating')): ?>
                                    <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                                        ⭐ <?php echo e(request('min_rating')); ?>+ stars
                                    </span>
                                <?php endif; ?>

                                <?php if(request('facilities')): ?>
                                    <?php $__currentLoopData = request('facilities'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $facility): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                                            🏠 <?php echo e($facility); ?>

                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>

                                <?php if(request('sort_by') && request('sort_by') !== 'latest'): ?>
                                    <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                                        📊 <?php echo e(ucfirst(str_replace('_', ' ', request('sort_by')))); ?>

                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Kos Grid -->
            <?php if($kos->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    <?php $__currentLoopData = $kos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $property): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="card group hover:shadow-2xl transition-all duration-300">
                            <!-- Image -->
                            <div class="relative h-48 bg-gray-200 overflow-hidden">
                                <?php if($property->foto && count($property->foto) > 0): ?>
                                    <img src="<?php echo e(asset('storage/' . $property->foto[0])); ?>" 
                                         alt="<?php echo e($property->nama_kos); ?>"
                                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                                <?php else: ?>
                                    <div class="w-full h-full flex items-center justify-center bg-gray-100">
                                        <span class="text-4xl text-gray-400">🏠</span>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Price Badge -->
                                <div class="absolute top-4 right-4 bg-white shadow-sm px-3 py-1 rounded-full border border-gray-200">
                                    <span class="font-bold text-gray-900">Rp <?php echo e(number_format($property->harga, 0, ',', '.')); ?></span>
                                    <span class="text-xs text-gray-600">/month</span>
                                </div>
                            </div>
                            
                            <div class="card-body">
                                <!-- Title and Location -->
                                <div class="mb-3">
                                    <h3 class="font-bold text-lg text-gray-900 mb-1"><?php echo e($property->nama_kos); ?></h3>
                                    <p class="text-gray-600 text-sm flex items-center">
                                        📍 <?php echo e(Str::limit($property->alamat, 50)); ?>

                                    </p>
                                </div>
                                
                                <!-- Facilities -->
                                <div class="mb-4">
                                    <p class="text-gray-700 text-sm">
                                        <?php echo e(Str::limit($property->fasilitas, 80)); ?>

                                    </p>
                                </div>
                                
                                <!-- Rating and Reviews -->
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center space-x-2">
                                        <?php if($property->reviews->count() > 0): ?>
                                            <div class="flex items-center">
                                                <span class="text-yellow-400">⭐</span>
                                                <span class="font-medium text-gray-900 ml-1">
                                                    <?php echo e(number_format($property->averageRating(), 1)); ?>

                                                </span>
                                                <span class="text-gray-600 text-sm ml-1">
                                                    (<?php echo e($property->reviews->count()); ?> reviews)
                                                </span>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-gray-500 text-sm">No reviews yet</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <!-- Action Button -->
                                <a href="<?php echo e(route('kos.show', $property)); ?>" 
                                   class="btn-primary w-full text-center block">
                                    View Details
                                </a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                
                <!-- Pagination -->
                <div class="flex justify-center">
                    <?php echo e($kos->withQueryString()->links()); ?>

                </div>
            <?php else: ?>
                <!-- Empty State -->
                <div class="card">
                    <div class="card-body text-center py-12">
                        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <span class="text-gray-400 text-4xl">🔍</span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">No properties found</h3>
                        <p class="text-gray-600 mb-6">
                            <?php if(request()->hasAny(['search', 'min_price', 'max_price'])): ?>
                                Try adjusting your search criteria or browse all available properties.
                            <?php else: ?>
                                There are no kos properties available at the moment.
                            <?php endif; ?>
                        </p>
                        <?php if(request()->hasAny(['search', 'min_price', 'max_price'])): ?>
                            <a href="<?php echo e(route('kos.index')); ?>" class="btn-secondary">
                                Clear Filters
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        function toggleFilters() {
            const filters = document.getElementById('advancedFilters');
            const icon = document.getElementById('filterToggleIcon');

            if (filters.classList.contains('hidden')) {
                filters.classList.remove('hidden');
                icon.textContent = '▲';
            } else {
                filters.classList.add('hidden');
                icon.textContent = '▼';
            }
        }

        function toggleFacilities() {
            const dropdown = document.getElementById('facilitiesDropdown');
            dropdown.classList.toggle('hidden');
        }

        function setPriceRange(min, max) {
            document.querySelector('input[name="min_price"]').value = min;
            document.querySelector('input[name="max_price"]').value = max || '';
            document.getElementById('searchForm').submit();
        }

        // Close facilities dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('facilitiesDropdown');
            const button = event.target.closest('button');

            if (!button || button.getAttribute('onclick') !== 'toggleFacilities()') {
                dropdown.classList.add('hidden');
            }
        });

        // Show advanced filters if any advanced filter is active
        document.addEventListener('DOMContentLoaded', function() {
            const hasAdvancedFilters = <?php echo e(request()->hasAny(['location', 'min_price', 'max_price', 'min_rating', 'facilities']) ? 'true' : 'false'); ?>;
            if (hasAdvancedFilters) {
                toggleFilters();
            }
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\Rentalin\resources\views/kos/index.blade.php ENDPATH**/ ?>
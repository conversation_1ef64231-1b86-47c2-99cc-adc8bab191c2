<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Kos extends Model
{
    use HasFactory;

    protected $table = 'kos';

    protected $fillable = [
        'pemilik_id',
        'nama_kos',
        'alamat',
        'harga',
        'fasilitas',
        'foto',
        'status',
        'latitude',
        'longitude',
    ];

    protected function casts(): array
    {
        return [
            'foto' => 'array',
            'status' => 'boolean',
            'harga' => 'decimal:2',
            'latitude' => 'decimal:8',
            'longitude' => 'decimal:8',
        ];
    }

    /**
     * Get the owner of this kos
     */
    public function owner()
    {
        return $this->belongsTo(User::class, 'pemilik_id');
    }

    /**
     * Get the bookings for this kos
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Get the reviews for this kos
     */
    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the average rating for this kos
     */
    public function averageRating()
    {
        return $this->reviews()->avg('rating');
    }

    /**
     * Scope to filter available kos
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope to filter by price range
     */
    public function scopePriceRange($query, $min, $max)
    {
        return $query->whereBetween('harga', [$min, $max]);
    }
}

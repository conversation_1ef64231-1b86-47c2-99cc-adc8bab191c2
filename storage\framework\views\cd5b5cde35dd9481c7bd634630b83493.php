<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex items-center justify-between">
            <div>
                <h2 class="font-bold text-2xl text-gray-900">
                    <?php echo e($kos->nama_kos); ?>

                </h2>
                <p class="text-gray-600 mt-1 flex items-center">
                    📍 <?php echo e($kos->alamat); ?>

                </p>
            </div>
            <div class="hidden sm:flex items-center space-x-3 px-6">
                
                <a href="<?php echo e(route('owner.kos.index')); ?>" class="btn-secondary">
                    ← Back to Properties
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php if(session('success')): ?>
                <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Property Photos -->
                    <?php if($kos->foto && count($kos->foto) > 0): ?>
                    <div class="card">
                        <div class="card-body p-0">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                <?php $__currentLoopData = $kos->foto; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $photo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="<?php echo e($index === 0 ? 'md:col-span-2' : ''); ?>">
                                        <img src="<?php echo e(asset('storage/' . $photo)); ?>" 
                                             alt="<?php echo e($kos->nama_kos); ?>"
                                             class="w-full <?php echo e($index === 0 ? 'h-64' : 'h-32'); ?> object-cover <?php echo e($index === 0 ? 'rounded-t-lg' : 'rounded-lg'); ?>">
                                    </div>
                                    <?php if($index >= 3): ?> <?php break; ?> <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <?php if(count($kos->foto) > 4): ?>
                                <div class="p-4 text-center">
                                    <span class="text-gray-600">+<?php echo e(count($kos->foto) - 4); ?> more photos</span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Property Details -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-xl font-bold text-gray-900">Property Details</h3>
                        </div>
                        <div class="card-body space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-2">Basic Information</h4>
                                    <div class="space-y-2">
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Property Name:</span>
                                            <span class="font-medium"><?php echo e($kos->nama_kos); ?></span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Monthly Rent:</span>
                                            <span class="font-bold text-green-600">Rp <?php echo e(number_format($kos->harga, 0, ',', '.')); ?></span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Status:</span>
                                            <span class="px-2 py-1 text-xs rounded-full <?php echo e($kos->status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                                <?php echo e($kos->status ? 'Available' : 'Not Available'); ?>

                                            </span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-2">Statistics</h4>
                                    <div class="space-y-2">
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Total Bookings:</span>
                                            <span class="font-medium"><?php echo e($kos->bookings_count ?? $kos->bookings->count()); ?></span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Total Reviews:</span>
                                            <span class="font-medium"><?php echo e($totalReviews); ?></span>
                                        </div>
                                        <?php if($averageRating): ?>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Average Rating:</span>
                                            <div class="flex items-center space-x-1">
                                                <span class="text-yellow-400">⭐</span>
                                                <span class="font-medium"><?php echo e(number_format($averageRating, 1)); ?></span>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-2">Full Address</h4>
                                <p class="text-gray-700"><?php echo e($kos->alamat); ?></p>
                            </div>
                            
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-2">Facilities & Amenities</h4>
                                <p class="text-gray-700 leading-relaxed"><?php echo e($kos->fasilitas); ?></p>
                            </div>

                            <?php if($kos->latitude && $kos->longitude): ?>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-2">Location Coordinates</h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <span class="text-gray-600">Latitude:</span>
                                        <span class="font-medium"><?php echo e($kos->latitude); ?></span>
                                    </div>
                                    <div>
                                        <span class="text-gray-600">Longitude:</span>
                                        <span class="font-medium"><?php echo e($kos->longitude); ?></span>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Recent Bookings -->
                    <?php if($kos->bookings->count() > 0): ?>
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-xl font-bold text-gray-900">Recent Bookings</h3>
                        </div>
                        <div class="card-body">
                            <div class="space-y-4">
                                <?php $__currentLoopData = $kos->bookings->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold">
                                                <?php echo e(substr($booking->user->name, 0, 1)); ?>

                                            </div>
                                            <div>
                                                <p class="font-medium text-gray-900"><?php echo e($booking->user->name); ?></p>
                                                <p class="text-sm text-gray-600"><?php echo e($booking->tanggal_mulai->format('M d, Y')); ?> - <?php echo e($booking->durasi); ?> months</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-bold text-gray-900">Rp <?php echo e(number_format($booking->total_harga, 0, ',', '.')); ?></p>
                                            <span class="px-2 py-1 text-xs rounded-full 
                                                <?php echo e($booking->status_pemesanan === 'confirmed' ? 'bg-green-100 text-green-800' : 
                                                   ($booking->status_pemesanan === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800')); ?>">
                                                <?php echo e(ucfirst($booking->status_pemesanan)); ?>

                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <?php if($kos->bookings->count() > 5): ?>
                                <div class="mt-4 text-center">
                                    <a href="<?php echo e(route('owner.bookings.index', ['kos_id' => $kos->id])); ?>" class="text-blue-600 hover:text-blue-800">
                                        View all bookings →
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
                        </div>
                        <div class="card-body space-y-3">
                            <a href="<?php echo e(route('owner.kos.edit', $kos)); ?>" class="w-full btn-primary text-center">
                                ✏️ Edit Property
                            </a>
                            <button onclick="toggleStatus()" class="w-full btn-secondary">
                                <?php echo e($kos->status ? '🔒 Mark Unavailable' : '🔓 Mark Available'); ?>

                            </button>
                            <form action="<?php echo e(route('owner.kos.destroy', $kos)); ?>" 
                                  method="POST" 
                                  class="w-full"
                                  onsubmit="return confirm('Are you sure you want to delete this property? This action cannot be undone.')">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors duration-200">
                                    🗑️ Delete Property
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Property Summary -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-lg font-semibold text-gray-900">Summary</h3>
                        </div>
                        <div class="card-body space-y-3">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600">Rp <?php echo e(number_format($kos->harga, 0, ',', '.')); ?></div>
                                <div class="text-sm text-gray-600">per month</div>
                            </div>
                            
                            <?php if($averageRating): ?>
                            <div class="text-center">
                                <div class="flex items-center justify-center space-x-1">
                                    <span class="text-yellow-400 text-xl">⭐</span>
                                    <span class="text-lg font-bold"><?php echo e(number_format($averageRating, 1)); ?></span>
                                </div>
                                <div class="text-sm text-gray-600"><?php echo e($totalReviews); ?> reviews</div>
                            </div>
                            <?php endif; ?>
                            
                            <div class="text-center">
                                <div class="text-lg font-bold text-blue-600"><?php echo e($kos->bookings->count()); ?></div>
                                <div class="text-sm text-gray-600">total bookings</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleStatus() {
            // This would typically make an AJAX request to toggle the status
            // For now, we'll redirect to edit page
            window.location.href = "<?php echo e(route('owner.kos.edit', $kos)); ?>";
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\Rentalin\resources\views/owner/kos/show.blade.php ENDPATH**/ ?>
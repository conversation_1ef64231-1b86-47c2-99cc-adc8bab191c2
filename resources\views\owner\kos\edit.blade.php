<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="font-bold text-2xl text-gray-900">
                    Edit Property ✏️
                </h2>
                <p class="text-gray-600 mt-1">Update your kos property details</p>
            </div>
            <div class="hidden sm:flex items-center space-x-3 px-6">
                <a href="{{ route('owner.kos.show', $kos) }}" class="btn-secondary">
                    ← Back to Property
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-8">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <form action="{{ route('owner.kos.update', $kos) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                @csrf
                @method('PUT')
                
                <!-- Basic Information -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold text-gray-900">Basic Information</h3>
                    </div>
                    <div class="card-body space-y-4">
                        <!-- Property Name -->
                        <div>
                            <label for="nama_kos" class="form-label">Property Name *</label>
                            <input type="text" 
                                   id="nama_kos" 
                                   name="nama_kos" 
                                   value="{{ old('nama_kos', $kos->nama_kos) }}"
                                   class="form-input" 
                                   placeholder="e.g., Kos Melati Indah"
                                   required>
                            @error('nama_kos')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Address -->
                        <div>
                            <label for="alamat" class="form-label">Full Address *</label>
                            <textarea id="alamat" 
                                      name="alamat" 
                                      rows="3"
                                      class="form-input" 
                                      placeholder="Enter the complete address including street, district, city"
                                      required>{{ old('alamat', $kos->alamat) }}</textarea>
                            @error('alamat')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Price -->
                        <div>
                            <label for="harga" class="form-label">Monthly Rent (Rp) *</label>
                            <input type="number"
                                   id="harga"
                                   name="harga"
                                   value="{{ old('harga', $kos->harga) }}"
                                   class="form-input"
                                   placeholder="e.g., 1500000"
                                   min="0"
                                   max="999999999999.99"
                                   step="0.01"
                                   required>
                            <p class="mt-1 text-sm text-gray-600">
                                Enter the monthly rent in Indonesian Rupiah (Rp). Example: 1500000 for Rp 1.5 million.
                            </p>
                            @error('harga')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Facilities & Amenities -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold text-gray-900">Facilities & Amenities</h3>
                    </div>
                    <div class="card-body">
                        <div>
                            <label for="fasilitas" class="form-label">Facilities Description *</label>
                            <textarea id="fasilitas" 
                                      name="fasilitas" 
                                      rows="5"
                                      class="form-input" 
                                      placeholder="Describe all facilities and amenities available (e.g., WiFi, AC, private bathroom, kitchen, parking, etc.)"
                                      required>{{ old('fasilitas', $kos->fasilitas) }}</textarea>
                            @error('fasilitas')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Current Photos -->
                @if($kos->foto && count($kos->foto) > 0)
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold text-gray-900">Current Photos</h3>
                    </div>
                    <div class="card-body">
                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                            @foreach($kos->foto as $photo)
                                <div class="relative group">
                                    <img src="{{ asset('storage/' . $photo) }}" 
                                         alt="Property photo"
                                         class="w-full h-24 object-cover rounded-lg">
                                    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
                                        <span class="text-white text-xs">Current Photo</span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif

                <!-- Add New Photos -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold text-gray-900">Add New Photos</h3>
                    </div>
                    <div class="card-body">
                        <div>
                            <label for="foto" class="form-label">Upload Additional Photos</label>
                            <input type="file" 
                                   id="foto" 
                                   name="foto[]" 
                                   multiple
                                   accept="image/*"
                                   class="form-input">
                            <p class="mt-1 text-sm text-gray-600">
                                You can upload additional photos. Supported formats: JPEG, PNG, JPG, GIF. Max size: 2MB per photo.
                            </p>
                            @error('foto')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            @error('foto.*')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Location (Optional) -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold text-gray-900">Location Coordinates (Optional)</h3>
                    </div>
                    <div class="card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="latitude" class="form-label">Latitude</label>
                                <input type="number" 
                                       id="latitude" 
                                       name="latitude" 
                                       value="{{ old('latitude', $kos->latitude) }}"
                                       step="any"
                                       class="form-input" 
                                       placeholder="e.g., -6.2088">
                                @error('latitude')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div>
                                <label for="longitude" class="form-label">Longitude</label>
                                <input type="number" 
                                       id="longitude" 
                                       name="longitude" 
                                       value="{{ old('longitude', $kos->longitude) }}"
                                       step="any"
                                       class="form-input" 
                                       placeholder="e.g., 106.8456">
                                @error('longitude')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                        <p class="mt-2 text-sm text-gray-600">
                            Adding coordinates will help tenants find your property on the map.
                        </p>
                    </div>
                </div>

                <!-- Status -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold text-gray-900">Availability Status</h3>
                    </div>
                    <div class="card-body">
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="status" 
                                   name="status" 
                                   value="1"
                                   {{ old('status', $kos->status) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:ring-blue-500">
                            <label for="status" class="ml-2 text-sm text-gray-700">
                                Property is available for rent
                            </label>
                        </div>
                        <p class="mt-1 text-sm text-gray-600">
                            Uncheck this if the property is not ready for rent yet.
                        </p>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-end space-x-4">
                    <a href="{{ route('owner.kos.show', $kos) }}" class="btn-secondary">
                        Cancel
                    </a>
                    <button type="submit" class="btn-primary">
                        Update Property
                    </button>
                </div>
            </form>
        </div>
    </div>
</x-app-layout>

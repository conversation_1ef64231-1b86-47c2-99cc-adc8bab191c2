<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'booking_id',
        'metode_pembayaran',
        'jumlah',
        'status',
        'tanggal',
        'transaction_id',
    ];

    protected function casts(): array
    {
        return [
            'tanggal' => 'date',
            'jumlah' => 'decimal:2',
            'status' => 'boolean',
        ];
    }

    /**
     * Get the booking for this payment
     */
    public function booking()
    {
        return $this->belongsTo(Booking::class);
    }

    /**
     * Check if payment is completed
     */
    public function isCompleted()
    {
        return $this->status === true;
    }

    /**
     * Check if payment is pending
     */
    public function isPending()
    {
        return $this->status === false;
    }

    /**
     * Scope to filter completed payments
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope to filter pending payments
     */
    public function scopePending($query)
    {
        return $query->where('status', false);
    }
}
